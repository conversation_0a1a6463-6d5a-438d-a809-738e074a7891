#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片下载工具 - GUI版本
提供简单的图形界面来选择文件和配置下载选项
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from datetime import datetime
from image_downloader import ImageDownloader

class ImageDownloaderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("图片下载工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 变量
        self.selected_file = tk.StringVar()
        self.use_unsplash = tk.BooleanVar(value=False)
        self.api_key = tk.StringVar()
        self.is_downloading = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="图片下载工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="选择输入文件", padding="10")
        file_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="TXT文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        file_entry = ttk.Entry(file_frame, textvariable=self.selected_file, state="readonly")
        file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        browse_btn = ttk.Button(file_frame, text="浏览", command=self.browse_file)
        browse_btn.grid(row=0, column=2)
        
        # 格式说明
        format_label = ttk.Label(file_frame, text="支持格式: 文件名|描述 或 文件名,描述 或 文件名 描述", 
                                font=("Arial", 9), foreground="gray")
        format_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # API配置区域
        api_frame = ttk.LabelFrame(main_frame, text="API配置 (可选)", padding="10")
        api_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        api_frame.columnconfigure(1, weight=1)
        
        unsplash_check = ttk.Checkbutton(api_frame, text="使用Unsplash API (获取高质量图片)", 
                                        variable=self.use_unsplash, command=self.toggle_api_key)
        unsplash_check.grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        ttk.Label(api_frame, text="API密钥:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        
        self.api_entry = ttk.Entry(api_frame, textvariable=self.api_key, show="*", state="disabled")
        self.api_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        help_btn = ttk.Button(api_frame, text="获取API密钥", command=self.show_api_help, state="disabled")
        help_btn.grid(row=1, column=2)
        self.help_btn = help_btn
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=3, pady=(0, 10))
        
        self.download_btn = ttk.Button(control_frame, text="开始下载", command=self.start_download)
        self.download_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="停止", command=self.stop_download, state="disabled")
        self.stop_btn.pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="下载日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, state="disabled")
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def browse_file(self):
        """浏览并选择文件"""
        filename = filedialog.askopenfilename(
            title="选择包含图片描述的TXT文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.selected_file.set(filename)
            self.log_message(f"已选择文件: {os.path.basename(filename)}")
    
    def toggle_api_key(self):
        """切换API密钥输入状态"""
        if self.use_unsplash.get():
            self.api_entry.config(state="normal")
            self.help_btn.config(state="normal")
        else:
            self.api_entry.config(state="disabled")
            self.help_btn.config(state="disabled")
    
    def show_api_help(self):
        """显示API密钥获取帮助"""
        help_text = """获取Unsplash API密钥步骤：

1. 访问 https://unsplash.com/developers
2. 注册账号并登录
3. 点击 "New Application"
4. 填写应用信息并同意条款
5. 创建后复制 "Access Key"
6. 将Access Key粘贴到密钥输入框

注意：免费账户每小时限制50次请求"""
        
        messagebox.showinfo("获取API密钥", help_text)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.config(state="normal")
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state="disabled")
        self.root.update_idletasks()
    
    def start_download(self):
        """开始下载"""
        # 验证输入
        if not self.selected_file.get():
            messagebox.showerror("错误", "请先选择一个TXT文件")
            return
        
        if not os.path.exists(self.selected_file.get()):
            messagebox.showerror("错误", "选择的文件不存在")
            return
        
        if self.use_unsplash.get() and not self.api_key.get().strip():
            messagebox.showerror("错误", "使用Unsplash API需要提供API密钥")
            return
        
        # 更新UI状态
        self.is_downloading = True
        self.download_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.progress.start()
        self.status_var.set("正在下载...")
        
        # 清空日志
        self.log_text.config(state="normal")
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state="disabled")
        
        # 在新线程中执行下载
        download_thread = threading.Thread(target=self.download_worker)
        download_thread.daemon = True
        download_thread.start()
    
    def download_worker(self):
        """下载工作线程"""
        try:
            # 创建下载器
            api_key = self.api_key.get().strip() if self.use_unsplash.get() else None
            downloader = CustomImageDownloader(api_key, self.log_message)
            
            # 开始下载
            self.log_message("=== 开始下载任务 ===")
            success_count = downloader.process_downloads(self.selected_file.get())
            
            if self.is_downloading:  # 检查是否被停止
                self.log_message(f"=== 下载完成! 成功下载 {success_count} 张图片 ===")
                self.root.after(0, lambda: self.status_var.set(f"下载完成! 成功 {success_count} 张"))
                messagebox.showinfo("完成", f"下载完成!\n成功下载 {success_count} 张图片")
            
        except Exception as e:
            self.log_message(f"下载过程中发生错误: {str(e)}")
            self.root.after(0, lambda: self.status_var.set("下载失败"))
            messagebox.showerror("错误", f"下载失败: {str(e)}")
        
        finally:
            # 恢复UI状态
            self.root.after(0, self.download_finished)
    
    def stop_download(self):
        """停止下载"""
        self.is_downloading = False
        self.log_message("用户取消下载...")
        self.status_var.set("已取消")
    
    def download_finished(self):
        """下载完成后的UI更新"""
        self.is_downloading = False
        self.download_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress.stop()
        if self.status_var.get() == "正在下载...":
            self.status_var.set("就绪")

class CustomImageDownloader(ImageDownloader):
    """自定义的图片下载器，支持GUI日志输出"""

    def __init__(self, unsplash_access_key=None, log_callback=None):
        super().__init__(unsplash_access_key)
        self.log_callback = log_callback
        self.success_count = 0

    def log(self, message):
        """输出日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)

    def search_unsplash(self, query, per_page=1):
        """
        重写search_unsplash方法，将所有print输出重定向到GUI日志
        """
        if not self.unsplash_access_key:
            return None

        # 清理API密钥，去除前后空格
        api_key = self.unsplash_access_key.strip()

        # 验证API密钥格式
        if len(api_key) < 20:
            self.log(f"警告: API密钥长度异常 ({len(api_key)} 字符)，请检查密钥是否正确")

        url = "https://api.unsplash.com/search/photos"
        headers = {
            "Authorization": f"Client-ID {api_key}",
            "User-Agent": "ImageDownloader/1.0",
            "Accept": "application/json"
        }

        # 清理和编码查询字符串
        clean_query = query.strip()
        if not clean_query:
            self.log("警告: 查询字符串为空")
            return None

        params = {
            "query": clean_query,
            "per_page": per_page,
            "orientation": "all",
            "content_filter": "low"
        }

        try:
            self.log(f"  正在搜索: '{clean_query}'")
            response = self.session.get(url, headers=headers, params=params, timeout=15)

            if response.status_code == 200:
                data = response.json()
                if data.get('results') and len(data['results']) > 0:
                    image_url = data['results'][0]['urls']['regular']
                    self.log(f"  找到图片: {data['results'][0].get('description', '无描述')[:50]}...")
                    return image_url
                else:
                    self.log(f"  未找到匹配的图片")
                    return None
            elif response.status_code == 401:
                self.log(f"Unsplash API错误 401: API密钥无效，请检查密钥是否正确")
                self.log(f"当前密钥长度: {len(api_key)} 字符")
            elif response.status_code == 403:
                self.log(f"Unsplash API错误 403: API访问被拒绝，可能是请求频率过高或权限不足")
            elif response.status_code == 400:
                self.log(f"Unsplash API错误 400: 请求参数错误")
                try:
                    error_data = response.json()
                    if 'errors' in error_data:
                        self.log(f"详细错误: {error_data['errors']}")
                except:
                    pass
                self.log(f"请求URL: {response.url}")
                self.log(f"查询参数: {clean_query}")
            else:
                self.log(f"Unsplash API错误: {response.status_code}")
                try:
                    error_text = response.text[:200]
                    self.log(f"响应内容: {error_text}")
                except:
                    pass

        except Exception as e:
            if "Timeout" in str(e):
                self.log(f"Unsplash API请求超时")
            elif "Connection" in str(e):
                self.log(f"Unsplash API连接错误，请检查网络连接")
            else:
                self.log(f"Unsplash搜索失败: {e}")

        return None

    def create_download_folder(self):
        """重写创建文件夹方法，使用GUI日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"downloaded_images_{timestamp}"

        if not os.path.exists(folder_name):
            os.makedirs(folder_name)
            self.log(f"创建下载文件夹: {folder_name}")

        return folder_name
    
    def process_downloads(self, input_file):
        """处理下载任务（重写以支持GUI）"""
        self.log("开始处理图片下载任务...")
        
        # 解析输入文件
        items = self.parse_input_file(input_file)
        if not items:
            self.log("没有找到有效的下载项目")
            return 0
        
        self.log(f"找到 {len(items)} 个下载项目")
        
        # 创建下载文件夹
        download_folder = self.create_download_folder()
        self.log(f"创建下载文件夹: {download_folder}")
        
        # 处理每个下载项目
        self.success_count = 0
        for i, (filename, description) in enumerate(items, 1):
            self.log(f"[{i}/{len(items)}] 处理: {filename} - {description}")
            
            # 清理文件名
            clean_name = self.clean_filename(filename)
            
            # 搜索图片URL
            image_url = None
            
            # 首先尝试Unsplash（如果有API密钥）
            if self.unsplash_access_key:
                self.log("  使用Unsplash搜索...")
                image_url = self.search_unsplash(description)
            
            # 如果Unsplash失败，使用备选方案
            if not image_url:
                self.log("  使用Lorem Picsum获取随机图片...")
                image_url = self.search_lorem_picsum(description)
            
            if image_url:
                # 确定文件扩展名
                if not clean_name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
                    clean_name += '.jpg'
                
                filepath = os.path.join(download_folder, clean_name)
                
                self.log(f"  下载图片到: {clean_name}")
                if self.download_image(image_url, filepath):
                    self.log(f"  ✓ 下载成功")
                    self.success_count += 1
                else:
                    self.log(f"  ✗ 下载失败")
            else:
                self.log(f"  ✗ 未找到合适的图片")
            
            # 添加延迟避免请求过快
            import time
            time.sleep(1)
        
        return self.success_count

def main():
    root = tk.Tk()
    app = ImageDownloaderGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
