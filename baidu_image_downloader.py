#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度图片下载工具
从txt文件读取文件名和图片描述，然后从百度图片搜索下载图片
"""

import os
import requests
import json
import re
from datetime import datetime
from urllib.parse import quote, unquote
import time
import random

class BaiduImageDownloader:
    def __init__(self):
        """
        初始化百度图片下载器
        """
        self.session = requests.Session()
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def clean_filename(self, filename):
        """
        清理文件名，移除非法字符
        """
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        filename = filename.strip()
        if not filename:
            filename = "unnamed"
        return filename
    
    def create_download_folder(self):
        """
        创建基于当前时间的下载文件夹
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"downloaded_images_{timestamp}"
        
        if not os.path.exists(folder_name):
            os.makedirs(folder_name)
            print(f"创建下载文件夹: {folder_name}")
        
        return folder_name
    
    def search_baidu_images(self, query, num_images=1):
        """
        使用百度图片搜索获取图片URL
        """
        try:
            # 构造百度图片搜索URL
            encoded_query = quote(query)
            search_url = f"https://image.baidu.com/search/acjson?tn=resultjson_com&logid=&ipn=rj&ct=201326592&is=&fp=result&fr=&word={encoded_query}&queryWord={encoded_query}&cl=2&lm=-1&ie=utf-8&oe=utf-8&adpicid=&st=-1&z=&ic=&hd=&latest=&copyright=&s=&se=&tab=&width=&height=&face=0&istype=2&qc=&nc=1&expermode=&nojc=&isAsync=&pn=0&rn={num_images}&gsm=1e"
            
            print(f"  正在搜索百度图片: '{query}'")
            
            # 添加随机延迟
            time.sleep(random.uniform(1, 2))
            
            response = self.session.get(search_url, timeout=10)
            
            if response.status_code == 200:
                # 解析JSON响应
                try:
                    data = response.json()
                    if 'data' in data and data['data']:
                        images = []
                        for item in data['data']:
                            if 'thumbURL' in item and item['thumbURL']:
                                # 优先使用原图URL，如果没有则使用缩略图
                                img_url = item.get('objURL', item.get('thumbURL'))
                                if img_url:
                                    images.append({
                                        'url': img_url,
                                        'title': item.get('fromPageTitleEnc', ''),
                                        'size': f"{item.get('width', 0)}x{item.get('height', 0)}"
                                    })
                        
                        if images:
                            print(f"  找到 {len(images)} 张图片")
                            return images
                        else:
                            print(f"  未找到有效的图片URL")
                            return []
                            
                except json.JSONDecodeError:
                    print(f"  解析百度响应失败")
                    return []
            else:
                print(f"  百度图片搜索失败: HTTP {response.status_code}")
                return []
                
        except Exception as e:
            print(f"  百度图片搜索异常: {e}")
            return []
    
    def download_image(self, url, filepath):
        """
        下载图片到指定路径
        """
        try:
            # 添加图片请求的特殊头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://image.baidu.com/',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
            }
            
            response = self.session.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                return True
            else:
                print(f"    下载失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"    下载图片失败: {e}")
            return False
    
    def parse_input_file(self, filepath):
        """
        解析输入的txt文件
        """
        items = []
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                if '|' in line:
                    parts = line.split('|', 1)
                elif ',' in line:
                    parts = line.split(',', 1)
                else:
                    parts = line.split(' ', 1)
                
                if len(parts) >= 2:
                    filename = parts[0].strip()
                    description = parts[1].strip()
                    items.append((filename, description))
                else:
                    print(f"警告: 第{line_num}行格式不正确，跳过: {line}")
            
            return items
            
        except FileNotFoundError:
            print(f"错误: 找不到文件 {filepath}")
            return []
        except Exception as e:
            print(f"读取文件失败: {e}")
            return []
    
    def process_downloads(self, input_file):
        """
        处理下载任务
        """
        print("=== 开始百度图片下载任务 ===")
        
        # 解析输入文件
        items = self.parse_input_file(input_file)
        if not items:
            print("没有找到有效的下载项目")
            return 0
        
        print(f"找到 {len(items)} 个下载项目")
        
        # 创建下载文件夹
        download_folder = self.create_download_folder()
        
        # 处理每个下载项目
        success_count = 0
        for i, (filename, description) in enumerate(items, 1):
            print(f"\n[{i}/{len(items)}] 处理: {filename} - {description}")
            
            # 清理文件名
            clean_name = self.clean_filename(filename)
            
            # 搜索图片
            images = self.search_baidu_images(description, num_images=3)
            
            if images:
                # 尝试下载第一张图片
                downloaded = False
                for j, img_info in enumerate(images):
                    img_url = img_info['url']
                    
                    # 确定文件扩展名
                    if not clean_name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
                        # 尝试从URL推断扩展名
                        if '.jpg' in img_url or '.jpeg' in img_url:
                            clean_name += '.jpg'
                        elif '.png' in img_url:
                            clean_name += '.png'
                        elif '.gif' in img_url:
                            clean_name += '.gif'
                        elif '.webp' in img_url:
                            clean_name += '.webp'
                        else:
                            clean_name += '.jpg'  # 默认
                    
                    filepath = os.path.join(download_folder, clean_name)
                    
                    print(f"  尝试下载第{j+1}张图片: {img_info.get('size', '未知尺寸')}")
                    if self.download_image(img_url, filepath):
                        print(f"  ✓ 下载成功: {clean_name}")
                        success_count += 1
                        downloaded = True
                        break
                    else:
                        print(f"  ✗ 下载失败，尝试下一张")
                
                if not downloaded:
                    print(f"  ✗ 所有图片下载失败")
            else:
                print(f"  ✗ 未找到图片")
            
            # 添加延迟避免请求过快
            time.sleep(random.uniform(2, 4))
        
        print(f"\n=== 下载完成! 成功下载 {success_count}/{len(items)} 张图片 ===")
        print(f"图片保存在文件夹: {download_folder}")
        return success_count

def main():
    print("=== 百度图片下载工具 ===")
    print("支持的输入格式:")
    print("1. 文件名|图片描述")
    print("2. 文件名,图片描述")
    print("3. 文件名 图片描述")
    print()
    
    # 获取输入文件
    input_file = input("请输入包含文件名和图片描述的txt文件路径: ").strip()
    if not input_file:
        input_file = "image_list.txt"  # 默认文件名
    
    # 创建下载器并开始处理
    downloader = BaiduImageDownloader()
    downloader.process_downloads(input_file)

if __name__ == "__main__":
    main()
