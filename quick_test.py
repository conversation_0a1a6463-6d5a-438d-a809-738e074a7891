import requests

def test_api():
    api_key = 'tGrp8gtEWuwWMXjuIRcDBO2iHenlOgW_g6kxcDOvESY'
    url = 'https://api.unsplash.com/search/photos'
    headers = {
        'Authorization': f'Client-ID {api_key}',
        'User-Agent': 'ImageDownloader/1.0'
    }
    params = {
        'query': 'nature',
        'per_page': 1
    }
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        print(f'状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'搜索结果: {data.get("total", 0)} 张图片')
            print('✅ API密钥有效!')
            return True
        else:
            print(f'❌ API错误: {response.status_code}')
            print(f'响应: {response.text[:200]}')
            return False
            
    except Exception as e:
        print(f'错误: {e}')
        return False

if __name__ == '__main__':
    test_api()
