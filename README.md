# 图片下载工具

这是一个Python图片下载工具，可以根据文本描述从开源图片网站下载图片。

## 功能特点

- 从txt文件读取文件名和图片描述
- 支持多种输入格式（|、,、空格分隔）
- 自动创建基于时间戳的下载文件夹
- 支持Unsplash API（高质量图片）
- 提供备选图片源（无需API密钥）
- 自动清理文件名中的非法字符
- 显示下载进度和结果统计

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 准备输入文件

创建一个txt文件，包含文件名和图片描述。支持以下格式：

```
# 使用 | 分隔符
sunset_beach|beautiful sunset over ocean beach
mountain_view|snow capped mountains landscape

# 使用 , 分隔符  
flower_garden,colorful spring flowers in garden
coffee_cup,steaming hot coffee cup on wooden table

# 使用空格分隔符（第一个词作为文件名）
dog_running happy golden retriever running in park
book_reading person reading book in cozy library
```

### 2. 运行程序

```bash
python image_downloader.py
```

程序会提示你：
1. 输入txt文件路径（默认：image_list.txt）
2. 是否使用Unsplash API（推荐，但需要API密钥）
3. 如果使用Unsplash，需要输入API密钥

### 3. 获取Unsplash API密钥（可选但推荐）

1. 访问 [Unsplash Developers](https://unsplash.com/developers)
2. 注册账号并创建应用
3. 获取Access Key
4. 在程序中输入API密钥

## 输出结果

- 程序会创建一个以当前时间命名的文件夹（格式：downloaded_images_YYYYMMDD_HHMMSS）
- 下载的图片会保存在该文件夹中，使用你指定的文件名
- 如果文件名没有扩展名，会自动添加.jpg
- 程序会显示下载进度和最终统计结果

## 示例输出

```
=== 图片下载工具 ===
请输入包含文件名和图片描述的txt文件路径: image_list.txt
是否使用Unsplash API？(y/n，需要API密钥): y
请输入Unsplash Access Key: your_api_key_here

开始处理图片下载任务...
找到 6 个下载项目
创建下载文件夹: downloaded_images_20241221_143022

[1/6] 处理: sunset_beach - beautiful sunset over ocean beach
  使用Unsplash搜索...
  下载图片到: downloaded_images_20241221_143022\sunset_beach.jpg
  ✓ 下载成功

...

下载完成! 成功下载 6/6 张图片
图片保存在文件夹: downloaded_images_20241221_143022
```

## 注意事项

- 如果没有Unsplash API密钥，程序会使用备选图片源（随机图片）
- 建议在文件名和描述之间使用明确的分隔符（| 或 ,）
- 程序会自动处理文件名中的非法字符
- 下载过程中会有1秒延迟，避免请求过于频繁
- 支持中文文件名和描述

## 故障排除

1. **网络连接问题**：确保网络连接正常
2. **API限制**：Unsplash免费账户有请求限制，如果超限可以稍后再试
3. **文件格式问题**：检查txt文件的编码是否为UTF-8
4. **权限问题**：确保程序有创建文件夹和写入文件的权限
