#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unsplash API测试工具
用于验证API密钥是否正确配置
"""

import requests
import json

def test_unsplash_api(api_key):
    """
    测试Unsplash API密钥是否有效
    """
    print("=== Unsplash API测试工具 ===")
    print(f"测试API密钥: {api_key[:10]}...{api_key[-5:] if len(api_key) > 15 else api_key}")
    print(f"密钥长度: {len(api_key)} 字符")
    print()
    
    # 清理API密钥
    api_key = api_key.strip()
    
    # 测试API连接
    url = "https://api.unsplash.com/search/photos"
    headers = {
        "Authorization": f"Client-ID {api_key}",
        "User-Agent": "ImageDownloader/1.0",
        "Accept": "application/json"
    }
    
    params = {
        "query": "nature",
        "per_page": 1,
        "orientation": "all"
    }
    
    try:
        print("正在测试API连接...")
        response = requests.get(url, headers=headers, params=params, timeout=10)
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"请求URL: {response.url}")
        print()
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API密钥有效!")
            print(f"搜索结果数量: {data.get('total', 0)}")
            
            if data.get('results'):
                first_result = data['results'][0]
                print(f"第一张图片:")
                print(f"  - ID: {first_result.get('id')}")
                print(f"  - 描述: {first_result.get('description', '无描述')}")
                print(f"  - 作者: {first_result.get('user', {}).get('name', '未知')}")
                print(f"  - 图片URL: {first_result.get('urls', {}).get('small', '无URL')}")
            
            # 检查API限制信息
            rate_limit = response.headers.get('X-Ratelimit-Limit')
            rate_remaining = response.headers.get('X-Ratelimit-Remaining')
            if rate_limit and rate_remaining:
                print(f"\nAPI限制信息:")
                print(f"  - 每小时限制: {rate_limit} 次请求")
                print(f"  - 剩余请求: {rate_remaining} 次")
            
            return True
            
        elif response.status_code == 401:
            print("❌ API密钥无效!")
            print("请检查:")
            print("1. 密钥是否正确复制（注意前后空格）")
            print("2. 是否使用了正确的Access Key（不是Secret Key）")
            print("3. 应用是否已经激活")
            
        elif response.status_code == 403:
            print("❌ API访问被拒绝!")
            print("可能原因:")
            print("1. 请求频率过高，请稍后再试")
            print("2. API密钥权限不足")
            print("3. 应用被暂停或限制")
            
        elif response.status_code == 400:
            print("❌ 请求参数错误!")
            try:
                error_data = response.json()
                print(f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应内容: {response.text[:500]}")
                
        else:
            print(f"❌ 未知错误: HTTP {response.status_code}")
            try:
                error_text = response.text[:500]
                print(f"响应内容: {error_text}")
            except:
                pass
        
        return False
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时!")
        print("请检查网络连接")
        return False
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误!")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. 是否需要代理设置")
        print("3. 防火墙是否阻止了连接")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print("此工具用于测试Unsplash API密钥是否配置正确")
    print("获取API密钥: https://unsplash.com/developers")
    print()
    
    api_key = input("请输入你的Unsplash Access Key: ").strip()
    
    if not api_key:
        print("错误: 未输入API密钥")
        return
    
    if len(api_key) < 20:
        print(f"警告: API密钥长度异常 ({len(api_key)} 字符)")
        print("Unsplash API密钥通常是40-50个字符的字母数字组合")
        confirm = input("是否继续测试? (y/n): ").lower()
        if not confirm.startswith('y'):
            return
    
    print()
    success = test_unsplash_api(api_key)
    
    print()
    if success:
        print("🎉 测试成功! 你可以在图片下载工具中使用这个API密钥")
    else:
        print("💡 建议:")
        print("1. 重新检查API密钥")
        print("2. 确认网络连接正常")
        print("3. 如果问题持续，可以尝试重新生成API密钥")

if __name__ == "__main__":
    main()
