#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度图片下载工具 - GUI版本
提供简单的图形界面来选择文件和下载百度图片
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from datetime import datetime
from baidu_image_downloader import BaiduImageDownloader

class BaiduImageGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("百度图片下载工具")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 变量
        self.selected_file = tk.StringVar()
        self.is_downloading = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="百度图片下载工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 说明文字
        info_label = ttk.Label(main_frame, text="🎉 无需API密钥，直接搜索百度图片！", 
                              font=("Arial", 10), foreground="green")
        info_label.grid(row=1, column=0, columnspan=3, pady=(0, 10))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="选择输入文件", padding="10")
        file_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        ttk.Label(file_frame, text="TXT文件:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        file_entry = ttk.Entry(file_frame, textvariable=self.selected_file, state="readonly")
        file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        browse_btn = ttk.Button(file_frame, text="浏览", command=self.browse_file)
        browse_btn.grid(row=0, column=2)
        
        # 格式说明
        format_label = ttk.Label(file_frame, text="支持格式: 文件名|描述 或 文件名,描述 或 文件名 描述", 
                                font=("Arial", 9), foreground="gray")
        format_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
        # 功能说明区域
        feature_frame = ttk.LabelFrame(main_frame, text="功能特点", padding="10")
        feature_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        features_text = """✅ 直接搜索百度图片，无需API密钥
✅ 支持中文搜索，结果更准确
✅ 自动尝试多张图片，提高成功率
✅ 智能文件名处理和格式识别"""
        
        features_label = ttk.Label(feature_frame, text=features_text, font=("Arial", 9))
        features_label.grid(row=0, column=0, sticky=tk.W)
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, columnspan=3, pady=(0, 10))
        
        self.download_btn = ttk.Button(control_frame, text="开始下载", command=self.start_download)
        self.download_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="停止", command=self.stop_download, state="disabled")
        self.stop_btn.pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 日志输出区域
        log_frame = ttk.LabelFrame(main_frame, text="下载日志", padding="10")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, state="disabled")
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def browse_file(self):
        """浏览并选择文件"""
        filename = filedialog.askopenfilename(
            title="选择包含图片描述的TXT文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.selected_file.set(filename)
            self.log_message(f"已选择文件: {os.path.basename(filename)}")
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.config(state="normal")
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state="disabled")
        self.root.update_idletasks()
    
    def start_download(self):
        """开始下载"""
        # 验证输入
        if not self.selected_file.get():
            messagebox.showerror("错误", "请先选择一个TXT文件")
            return
        
        if not os.path.exists(self.selected_file.get()):
            messagebox.showerror("错误", "选择的文件不存在")
            return
        
        # 更新UI状态
        self.is_downloading = True
        self.download_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.progress.start()
        self.status_var.set("正在下载...")
        
        # 清空日志
        self.log_text.config(state="normal")
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state="disabled")
        
        # 在新线程中执行下载
        download_thread = threading.Thread(target=self.download_worker)
        download_thread.daemon = True
        download_thread.start()
    
    def download_worker(self):
        """下载工作线程"""
        try:
            # 创建下载器
            downloader = CustomBaiduDownloader(self.log_message)
            
            # 开始下载
            self.log_message("=== 开始百度图片下载任务 ===")
            success_count = downloader.process_downloads(self.selected_file.get())
            
            if self.is_downloading:  # 检查是否被停止
                self.log_message(f"=== 下载完成! 成功下载 {success_count} 张图片 ===")
                self.root.after(0, lambda: self.status_var.set(f"下载完成! 成功 {success_count} 张"))
                messagebox.showinfo("完成", f"下载完成!\n成功下载 {success_count} 张图片")
            
        except Exception as e:
            self.log_message(f"下载过程中发生错误: {str(e)}")
            self.root.after(0, lambda: self.status_var.set("下载失败"))
            messagebox.showerror("错误", f"下载失败: {str(e)}")
        
        finally:
            # 恢复UI状态
            self.root.after(0, self.download_finished)
    
    def stop_download(self):
        """停止下载"""
        self.is_downloading = False
        self.log_message("用户取消下载...")
        self.status_var.set("已取消")
    
    def download_finished(self):
        """下载完成后的UI更新"""
        self.is_downloading = False
        self.download_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.progress.stop()
        if self.status_var.get() == "正在下载...":
            self.status_var.set("就绪")

class CustomBaiduDownloader(BaiduImageDownloader):
    """自定义的百度图片下载器，支持GUI日志输出"""
    
    def __init__(self, log_callback=None):
        super().__init__()
        self.log_callback = log_callback
        self.success_count = 0
    
    def log(self, message):
        """输出日志"""
        if self.log_callback:
            self.log_callback(message)
        else:
            print(message)
    
    def create_download_folder(self):
        """重写创建文件夹方法，使用GUI日志"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"downloaded_images_{timestamp}"
        
        if not os.path.exists(folder_name):
            os.makedirs(folder_name)
            self.log(f"创建下载文件夹: {folder_name}")
        
        return folder_name
    
    def search_baidu_images(self, query, num_images=1):
        """重写搜索方法，使用GUI日志"""
        try:
            from urllib.parse import quote
            import json
            import time
            import random
            
            # 构造百度图片搜索URL
            encoded_query = quote(query)
            search_url = f"https://image.baidu.com/search/acjson?tn=resultjson_com&logid=&ipn=rj&ct=201326592&is=&fp=result&fr=&word={encoded_query}&queryWord={encoded_query}&cl=2&lm=-1&ie=utf-8&oe=utf-8&adpicid=&st=-1&z=&ic=&hd=&latest=&copyright=&s=&se=&tab=&width=&height=&face=0&istype=2&qc=&nc=1&expermode=&nojc=&isAsync=&pn=0&rn={num_images}&gsm=1e"
            
            self.log(f"  正在搜索百度图片: '{query}'")
            
            # 添加随机延迟
            time.sleep(random.uniform(1, 2))
            
            response = self.session.get(search_url, timeout=10)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if 'data' in data and data['data']:
                        images = []
                        for item in data['data']:
                            if 'thumbURL' in item and item['thumbURL']:
                                img_url = item.get('objURL', item.get('thumbURL'))
                                if img_url:
                                    images.append({
                                        'url': img_url,
                                        'title': item.get('fromPageTitleEnc', ''),
                                        'size': f"{item.get('width', 0)}x{item.get('height', 0)}"
                                    })
                        
                        if images:
                            self.log(f"  找到 {len(images)} 张图片")
                            return images
                        else:
                            self.log(f"  未找到有效的图片URL")
                            return []
                            
                except json.JSONDecodeError:
                    self.log(f"  解析百度响应失败")
                    return []
            else:
                self.log(f"  百度图片搜索失败: HTTP {response.status_code}")
                return []
                
        except Exception as e:
            self.log(f"  百度图片搜索异常: {e}")
            return []
    
    def process_downloads(self, input_file):
        """处理下载任务（重写以支持GUI）"""
        self.log("开始处理图片下载任务...")
        
        # 解析输入文件
        items = self.parse_input_file(input_file)
        if not items:
            self.log("没有找到有效的下载项目")
            return 0
        
        self.log(f"找到 {len(items)} 个下载项目")
        
        # 创建下载文件夹
        download_folder = self.create_download_folder()
        
        # 处理每个下载项目
        self.success_count = 0
        for i, (filename, description) in enumerate(items, 1):
            self.log(f"[{i}/{len(items)}] 处理: {filename} - {description}")
            
            # 清理文件名
            clean_name = self.clean_filename(filename)
            
            # 搜索图片
            images = self.search_baidu_images(description, num_images=3)
            
            if images:
                # 尝试下载第一张图片
                downloaded = False
                for j, img_info in enumerate(images):
                    img_url = img_info['url']
                    
                    # 确定文件扩展名
                    if not clean_name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
                        if '.jpg' in img_url or '.jpeg' in img_url:
                            clean_name += '.jpg'
                        elif '.png' in img_url:
                            clean_name += '.png'
                        elif '.gif' in img_url:
                            clean_name += '.gif'
                        elif '.webp' in img_url:
                            clean_name += '.webp'
                        else:
                            clean_name += '.jpg'
                    
                    filepath = os.path.join(download_folder, clean_name)
                    
                    self.log(f"  尝试下载第{j+1}张图片: {img_info.get('size', '未知尺寸')}")
                    if self.download_image(img_url, filepath):
                        self.log(f"  ✓ 下载成功: {clean_name}")
                        self.success_count += 1
                        downloaded = True
                        break
                    else:
                        self.log(f"  ✗ 下载失败，尝试下一张")
                
                if not downloaded:
                    self.log(f"  ✗ 所有图片下载失败")
            else:
                self.log(f"  ✗ 未找到图片")
            
            # 添加延迟避免请求过快
            import time
            import random
            time.sleep(random.uniform(2, 4))
        
        return self.success_count

def main():
    root = tk.Tk()
    app = BaiduImageGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
