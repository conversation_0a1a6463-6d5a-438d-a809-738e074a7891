#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片下载工具
从txt文件读取文件名和图片描述，然后从开源图片网站下载图片
"""

import os
import requests
import json
import re
from datetime import datetime
from urllib.parse import quote
import time

class ImageDownloader:
    def __init__(self, unsplash_access_key=None):
        """
        初始化图片下载器
        :param unsplash_access_key: Unsplash API密钥（可选）
        """
        self.unsplash_access_key = unsplash_access_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def clean_filename(self, filename):
        """
        清理文件名，移除非法字符
        """
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 移除前后空格
        filename = filename.strip()
        # 确保不为空
        if not filename:
            filename = "unnamed"
        return filename
    
    def create_download_folder(self):
        """
        创建基于当前时间的下载文件夹
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"downloaded_images_{timestamp}"
        
        if not os.path.exists(folder_name):
            os.makedirs(folder_name)
            print(f"创建下载文件夹: {folder_name}")
        
        return folder_name
    
    def search_unsplash(self, query, per_page=1):
        """
        使用Unsplash API搜索图片
        """
        if not self.unsplash_access_key:
            return None

        # 清理API密钥，去除前后空格
        api_key = self.unsplash_access_key.strip()

        # 验证API密钥格式（Unsplash密钥通常是40-50个字符的字母数字组合）
        if len(api_key) < 20:
            print(f"警告: API密钥长度异常 ({len(api_key)} 字符)，请检查密钥是否正确")

        url = "https://api.unsplash.com/search/photos"
        headers = {
            "Authorization": f"Client-ID {api_key}",
            "User-Agent": "ImageDownloader/1.0",
            "Accept": "application/json"
        }

        # 清理和编码查询字符串
        clean_query = query.strip()
        if not clean_query:
            print("警告: 查询字符串为空")
            return None

        params = {
            "query": clean_query,
            "per_page": per_page,
            "content_filter": "low"  # 添加内容过滤
            # 移除orientation参数，让API返回所有方向的图片
        }

        try:
            print(f"  正在搜索: '{clean_query}'")
            response = self.session.get(url, headers=headers, params=params, timeout=15)

            if response.status_code == 200:
                data = response.json()
                if data.get('results') and len(data['results']) > 0:
                    image_url = data['results'][0]['urls']['regular']
                    print(f"  找到图片: {data['results'][0].get('description', '无描述')[:50]}...")
                    return image_url
                else:
                    print(f"  未找到匹配的图片")
                    return None
            elif response.status_code == 401:
                print(f"Unsplash API错误 401: API密钥无效，请检查密钥是否正确")
                print(f"当前密钥长度: {len(api_key)} 字符")
            elif response.status_code == 403:
                print(f"Unsplash API错误 403: API访问被拒绝，可能是请求频率过高或权限不足")
            elif response.status_code == 400:
                print(f"Unsplash API错误 400: 请求参数错误")
                try:
                    error_data = response.json()
                    if 'errors' in error_data:
                        print(f"详细错误: {error_data['errors']}")
                except:
                    pass
                print(f"请求URL: {response.url}")
                print(f"查询参数: {clean_query}")
            else:
                print(f"Unsplash API错误: {response.status_code}")
                try:
                    error_text = response.text[:200]
                    print(f"响应内容: {error_text}")
                except:
                    pass

        except requests.exceptions.Timeout:
            print(f"Unsplash API请求超时")
        except requests.exceptions.ConnectionError:
            print(f"Unsplash API连接错误，请检查网络连接")
        except Exception as e:
            print(f"Unsplash搜索失败: {e}")

        return None
    
    def search_pixabay(self, query):
        """
        使用Pixabay免费API搜索图片（无需API密钥的备选方案）
        """
        # 这里使用一个简单的方法，实际使用时建议申请Pixabay API密钥
        try:
            # 构造搜索URL（这是一个示例，实际可能需要调整）
            search_url = f"https://pixabay.com/api/?key=YOUR_PIXABAY_KEY&q={quote(query)}&image_type=photo&per_page=3"
            # 由于没有API密钥，我们使用一个备选方案
            return None
        except Exception as e:
            print(f"Pixabay搜索失败: {e}")
            return None
    
    def search_lorem_picsum(self, query):
        """
        使用Lorem Picsum作为备选方案（随机图片）
        """
        # Lorem Picsum提供随机图片，虽然不能根据内容搜索，但可以作为备选
        try:
            # 生成一个基于查询的随机种子
            seed = abs(hash(query)) % 1000
            url = f"https://picsum.photos/800/600?random={seed}"
            return url
        except Exception as e:
            print(f"Lorem Picsum获取失败: {e}")
            return None
    
    def download_image(self, url, filepath):
        """
        下载图片到指定路径
        """
        try:
            response = self.session.get(url, timeout=30)
            if response.status_code == 200:
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                return True
            else:
                print(f"下载失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            print(f"下载图片失败: {e}")
            return False
    
    def parse_input_file(self, filepath):
        """
        解析输入的txt文件
        支持格式：
        1. 文件名|图片描述
        2. 文件名,图片描述
        3. 文件名 图片描述（空格分隔，第一个词作为文件名）
        """
        items = []
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#'):  # 跳过空行和注释
                    continue
                
                # 尝试不同的分隔符
                if '|' in line:
                    parts = line.split('|', 1)
                elif ',' in line:
                    parts = line.split(',', 1)
                else:
                    # 空格分隔，第一个词作为文件名
                    parts = line.split(' ', 1)
                
                if len(parts) >= 2:
                    filename = parts[0].strip()
                    description = parts[1].strip()
                    items.append((filename, description))
                else:
                    print(f"警告: 第{line_num}行格式不正确，跳过: {line}")
            
            return items
            
        except FileNotFoundError:
            print(f"错误: 找不到文件 {filepath}")
            return []
        except Exception as e:
            print(f"读取文件失败: {e}")
            return []
    
    def process_downloads(self, input_file):
        """
        处理下载任务
        """
        print("开始处理图片下载任务...")
        
        # 解析输入文件
        items = self.parse_input_file(input_file)
        if not items:
            print("没有找到有效的下载项目")
            return
        
        print(f"找到 {len(items)} 个下载项目")
        
        # 创建下载文件夹
        download_folder = self.create_download_folder()
        
        # 处理每个下载项目
        success_count = 0
        for i, (filename, description) in enumerate(items, 1):
            print(f"\n[{i}/{len(items)}] 处理: {filename} - {description}")
            
            # 清理文件名
            clean_name = self.clean_filename(filename)
            
            # 搜索图片URL
            image_url = None
            
            # 首先尝试Unsplash（如果有API密钥）
            if self.unsplash_access_key:
                print("  使用Unsplash搜索...")
                image_url = self.search_unsplash(description)
            
            # 如果Unsplash失败，使用备选方案
            if not image_url:
                print("  使用Lorem Picsum获取随机图片...")
                image_url = self.search_lorem_picsum(description)
            
            if image_url:
                # 确定文件扩展名
                if not clean_name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
                    clean_name += '.jpg'
                
                filepath = os.path.join(download_folder, clean_name)
                
                print(f"  下载图片到: {filepath}")
                if self.download_image(image_url, filepath):
                    print(f"  ✓ 下载成功")
                    success_count += 1
                else:
                    print(f"  ✗ 下载失败")
            else:
                print(f"  ✗ 未找到合适的图片")
            
            # 添加延迟避免请求过快
            time.sleep(1)
        
        print(f"\n下载完成! 成功下载 {success_count}/{len(items)} 张图片")
        print(f"图片保存在文件夹: {download_folder}")

def main():
    print("=== 图片下载工具 ===")
    print("支持的输入格式:")
    print("1. 文件名|图片描述")
    print("2. 文件名,图片描述")
    print("3. 文件名 图片描述")
    print()
    
    # 获取输入文件
    input_file = input("请输入包含文件名和图片描述的txt文件路径: ").strip()
    if not input_file:
        input_file = "image_list.txt"  # 默认文件名
    
    # 询问是否使用Unsplash API
    use_unsplash = input("是否使用Unsplash API？(y/n，需要API密钥): ").lower().startswith('y')
    unsplash_key = None
    
    if use_unsplash:
        unsplash_key = input("请输入Unsplash Access Key: ").strip()
        if not unsplash_key:
            print("未提供API密钥，将使用备选图片源")
            unsplash_key = None
    
    # 创建下载器并开始处理
    downloader = ImageDownloader(unsplash_key)
    downloader.process_downloads(input_file)

if __name__ == "__main__":
    main()
