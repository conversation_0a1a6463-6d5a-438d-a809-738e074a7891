#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片下载工具启动器
用户可以选择使用命令行版本或GUI版本
"""

import sys
import os

def main():
    print("=== 图片下载工具 ===")
    print("请选择运行模式:")
    print("1. 百度图片下载 - GUI版本 (推荐，无需API)")
    print("2. 百度图片下载 - 命令行版本")
    print("3. Unsplash图片下载 - GUI版本 (需要API密钥)")
    print("4. Unsplash图片下载 - 命令行版本")
    print("5. 退出")

    while True:
        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == "1":
            print("启动百度图片下载GUI版本...")
            try:
                from baidu_image_gui import main as baidu_gui_main
                baidu_gui_main()
            except ImportError as e:
                print(f"启动失败: {e}")
                print("请确保已安装所有依赖: pip install -r requirements.txt")
            except Exception as e:
                print(f"运行时发生错误: {e}")
            break

        elif choice == "2":
            print("启动百度图片下载命令行版本...")
            try:
                from baidu_image_downloader import main as baidu_cli_main
                baidu_cli_main()
            except ImportError as e:
                print(f"启动失败: {e}")
                print("请确保已安装所有依赖: pip install -r requirements.txt")
            except Exception as e:
                print(f"运行时发生错误: {e}")
            break

        elif choice == "3":
            print("启动Unsplash GUI版本...")
            try:
                from image_downloader_gui import main as gui_main
                gui_main()
            except ImportError as e:
                print(f"启动GUI失败: {e}")
                print("请确保已安装所有依赖: pip install -r requirements.txt")
            except Exception as e:
                print(f"运行GUI时发生错误: {e}")
            break

        elif choice == "4":
            print("启动Unsplash命令行版本...")
            try:
                from image_downloader import main as cli_main
                cli_main()
            except ImportError as e:
                print(f"启动命令行版本失败: {e}")
                print("请确保已安装所有依赖: pip install -r requirements.txt")
            except Exception as e:
                print(f"运行命令行版本时发生错误: {e}")
            break

        elif choice == "5":
            print("再见!")
            sys.exit(0)

        else:
            print("无效选择，请输入 1-5")

if __name__ == "__main__":
    main()
